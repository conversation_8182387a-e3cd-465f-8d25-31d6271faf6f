import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';
import { ButtonModule } from 'cax-design-system/button';
import { DividerModule } from 'cax-design-system/divider';

@Component({
    selector: 'app-filters-list-panel',
    standalone: true,
    imports: [DividerModule, ButtonModule, CommonModule],
    templateUrl: './filters-list-panel.component.html',
    styleUrl: './filters-list-panel.component.scss',
})
export class FiltersListPanelComponent {
    @Input() filtersApplied: any = {};
    public object = Object;

    getEntries(obj: Record<string, any>): [string, any][] {
        return Object.entries(obj);
    }

    getColumnName(name: string) {
        return BatchColumnData.find(col => col.field === name)?.header;
    }

    getFilterString(filter: any) {
        return (
            [
                ...commonFilterOptions,
                ...stringFilterOptions,
                ...numberFilterOptions,
                ...dateFilterOptions,
            ].find(opt => opt.key === filter.matchMode)?.label +
            (filter.value ? ` ${filter.value}` : '')
        );
    }

    clearFilter() {
        console.log('event');
    }

    removeAllFilters() {
        console.log('remove');
    }
}
