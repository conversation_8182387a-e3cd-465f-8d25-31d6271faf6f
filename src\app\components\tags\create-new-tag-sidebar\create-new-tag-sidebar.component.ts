import {
    Component,
    EventEmitter,
    Output,
    Input,
    OnInit,
    CUSTOM_ELEMENTS_SCHEMA,
    ChangeDetectorRef,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { InputTextModule } from 'cax-design-system/inputtext';
import { InputTextareaModule } from 'cax-design-system/inputtextarea';
import { ButtonModule } from 'cax-design-system/button';
import { ChipModule } from 'cax-design-system/chip';
import { ColorPickerModule } from 'cax-design-system/colorpicker';
import { SidebarModule } from 'cax-design-system/sidebar';
import { CommonModule } from '@angular/common';
import { Tag } from '../../../test-data/tags';

type RequiredTag = Omit<Tag, 'customStyle'> & {
    customStyle: NonNullable<Tag['customStyle']>;
};

@Component({
    selector: 'app-create-new-tag-sidebar',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        InputTextModule,
        InputTextareaModule,
        ButtonModule,
        ChipModule,
        ColorPickerModule,
        SidebarModule,
    ],
    templateUrl: './create-new-tag-sidebar.component.html',
    styleUrl: './create-new-tag-sidebar.component.scss',
    schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class CreateNewTagSidebarComponent implements OnInit {
    @Input() set visible(value: boolean) {
        if (!value) {
            this.resetForm();
        }
        this._visible = value;
    }
    get visible(): boolean {
        return this._visible;
    }

    @Input() set tag(value: Tag | null) {
        if (value) {
            this.isEditMode = true;
            this.editedTag = {
                ...value,
                customStyle: {
                    color: value.customStyle?.color || '#FFFFFF',
                    backgroundColor: value.customStyle?.backgroundColor || '#5946b9',
                }
            };
        } else {
            this.isEditMode = false;
            this.resetForm();
        }
    }

    @Output() visibleChange = new EventEmitter<boolean>();
    @Output() close = new EventEmitter<void>();
    @Output() createTag = new EventEmitter<Pick<Tag, 'name' | 'description' | 'customStyle'>>();
    @Output() updateTag = new EventEmitter<Tag>();

    private _visible = false;
    isEditMode = false;

    editedTag: RequiredTag = {
        id: '',
        name: '',
        description: '',
        dateModified: '',
        modifiedBy: '',
        dateCreated: '',
        createdBy: '',
        status: 'Active',
        isActive: true,
        isRemovable: true,
        history: [],
        severity: 'custom',
        customStyle: {
            color: '#FFFFFF',
            backgroundColor: '#5946b9',
        }
    };

    constructor(private cdr: ChangeDetectorRef) {}

    ngOnInit() {
        this.resetForm();
    }

    onCancel() {
        this.visible = false;
        this.visibleChange.emit(false);
        this.close.emit();
    }

    onSave() {
        if (this.isValidForm()) {
            if (this.isEditMode) {
                this.updateTag.emit({
                    ...this.editedTag,
                    customStyle: {
                        color: this.editedTag.customStyle.color,
                        backgroundColor: this.editedTag.customStyle.backgroundColor
                    }
                });
            } else {
                this.createTag.emit({
                    name: this.editedTag.name,
                    description: this.editedTag.description,
                    customStyle: {
                        color: this.editedTag.customStyle.color,
                        backgroundColor: this.editedTag.customStyle.backgroundColor
                    }
                });
            }
            this.visible = false;
            this.visibleChange.emit(false);
        }
    }

    onTextColorChange(color: string) {
        this.editedTag.customStyle.color = color;
        this.cdr.markForCheck();
    }

    onBackgroundColorChange(color: string) {
        this.editedTag.customStyle.backgroundColor = color;
        this.cdr.markForCheck();
    }

    isValidForm(): boolean {
        return this.editedTag.name.trim().length > 0;
    }

    private resetForm() {
        this.editedTag = {
            id: '',
            name: '',
            description: '',
            dateModified: '',
            modifiedBy: '',
            dateCreated: '',
            createdBy: '',
            status: 'Active',
            isActive: true,
            isRemovable: true,
            history: [],
            severity: 'custom',
            customStyle: {
                color: '#FFFFFF',
                backgroundColor: '#5946b9',
            }
        };
        this.isEditMode = false;
        this.cdr.markForCheck();
    }
}
