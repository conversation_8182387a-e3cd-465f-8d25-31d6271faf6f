<cax-sidebar
    [(visible)]="visible"
    position="right"
    [headerText]="isEditMode ? 'Edit Tag' : 'Create New Tag'"
    [style]="{ width: '558px' }"
    (onHide)="onCancel()">
    <div class="sidebar-content">
        <div class="create-tag-container">
            <cax-inputtext
                [label]="'Tag Name'"
                [placeholder]="'Enter tag name within 30 characters'"
                [(ngModel)]="editedTag.name">
            </cax-inputtext>

            <cax-inputtextarea
                [label]="'Tag Description (Optional)'"
                [placeholder]="
                    'Describe the purpose of your tag within 100 words'
                "
                [(ngModel)]="editedTag.description">
            </cax-inputtextarea>

            <div class="color-pickers">
                <div class="text-color-picker">
                    <label for="text-color-picker">Text Color</label>
                    <cax-colorPicker
                        #textColorPicker
                        id="text-color-picker"
                        [(ngModel)]="editedTag.customStyle.color"
                        [inline]="true"
                        [format]="'hex'"
                        (ngModelChange)="onTextColorChange($event)">
                    </cax-colorPicker>
                    <div class="color-value">
                        {{ editedTag.customStyle.color }}
                    </div>
                </div>

                <div class="background-color-picker">
                    <label for="background-color-picker"
                        >Background Color</label
                    >
                    <cax-colorPicker
                        #bgColorPicker
                        id="background-color-picker"
                        [(ngModel)]="editedTag.customStyle.backgroundColor"
                        [inline]="true"
                        [format]="'hex'"
                        (ngModelChange)="onBackgroundColorChange($event)">
                    </cax-colorPicker>
                    <div class="color-value">
                        {{ editedTag.customStyle.backgroundColor }}
                    </div>
                </div>
            </div>

            <div class="tag-preview">
                <div class="preview-heading">Tag Preview</div>
                <div class="preview-container">
                    <button
                        class="preview-tag"
                        [style]="{
                            color: editedTag.customStyle.color,
                            backgroundColor:
                                editedTag.customStyle.backgroundColor,
                        }">
                        {{ editedTag.name || 'Priority M' }}
                    </button>
                </div>
            </div>
        </div>
        <div class="button-container">
            <cax-button
                class="half-width-button"
                [label]="isEditMode ? 'Discard Changes' : 'Cancel'"
                [outlined]="true"
                severity="secondary"
                (click)="onCancel()">
            </cax-button>

            <cax-button
                class="half-width-button"
                [label]="isEditMode ? 'Save Changes' : 'Create Tag'"
                (click)="onSave()"
                [disabled]="!isValidForm()">
            </cax-button>
        </div>
    </div>
</cax-sidebar>
