import { ChangeDetectorRef, Component, Input, OnInit, ViewChild } from '@angular/core';
import { TabViewModule } from 'cax-design-system/tabview';
import { ButtonModule } from 'cax-design-system/button';
import { TableModule } from 'cax-design-system/table';
import { SplitButtonModule } from 'cax-design-system/splitbutton';
import { PaginatorModule } from 'cax-design-system/paginator';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BatchRowsComponent } from './batch-rows/batch-rows.component';
import { SidebarModule } from 'cax-design-system/sidebar';
import { OverlayPanelModule } from 'cax-design-system/overlaypanel';
import { ChipModule } from 'cax-design-system/chip';
import { AutoCompleteModule } from 'cax-design-system/autocomplete';
import { ConfirmationService } from 'cax-design-system/api';
import { ConfirmDialogModule } from 'cax-design-system/confirmdialog';
import { BadgeModule } from 'cax-design-system/badge';
import { TableconfigurationModule } from 'cax-design-system/tableconfiguration';
import { ColumnListSidebarComponent } from './column-list-sidebar/column-list-sidebar.component';
import { UploadBatchSidebarComponent } from './upload-batch-sidebar/upload-batch-sidebar.component';
import { FiltersListPanelComponent } from './filters-list-panel/filters-list-panel.component';
import { TimelineModule } from 'cax-design-system/timeline';
import { DialogModule } from 'cax-design-system/dialog';
import { CommentboxModule } from 'cax-design-system/commentbox';
import { AvatarModule } from 'cax-design-system/avatar';
import { ReworkDialogComponent } from './rework-dialog/rework-dialog.component';
import { InputTextModule } from 'cax-design-system/inputtext';
import { DropdownModule } from 'cax-design-system/dropdown';
import { Calendar } from 'cax-design-system/calendar';
import { ActivatedRoute, Router } from '@angular/router';
import { CreateNewTagSidebarComponent } from '../tags/create-new-tag-sidebar/create-new-tag-sidebar.component';
import { DividerModule } from 'cax-design-system/divider';

// Service imports
import { HomeService } from '../../../services/home.service';
import { CommentsService } from '../../../services/comments.service';
import { UserService } from '../../../services/user.service';
import { ProductsService } from '../../../services/products.service';
import { ProductDetailsService } from '../../../services/product-details.service';
import { ReviewService } from '../../../services/review.service';

interface OverlayTag {
  name: string;
  severity: ChipSeverity;
}
type ChipSeverity = 'primary' | 'secondary' | 'success'  | 'warning' | 'error';
@Component({
    selector: 'app-batches',
    standalone: true,
    imports: [
        TabViewModule,
        ButtonModule,
        TableModule,
        CommonModule,
        FormsModule,
        SplitButtonModule,
        PaginatorModule,
        SidebarModule,
        OverlayPanelModule,
        ChipModule,
        AutoCompleteModule,
        ConfirmDialogModule,
        BadgeModule,
        TableconfigurationModule,
        ColumnListSidebarComponent,
        UploadBatchSidebarComponent,
        FiltersListPanelComponent,
        TimelineModule,
        DialogModule,
        CommentboxModule,
        AvatarModule,
        ReworkDialogComponent,
        InputTextModule,
        DropdownModule,
        SplitButtonModule,
        Calendar,
        FormsModule,
        CreateNewTagSidebarComponent,
         DividerModule,
    ],
    templateUrl: './batches.component.html',
    styleUrl: './batches.component.scss',
    providers: [ConfirmationService],
})

export class BatchesComponent implements OnInit {
    public object = Object;
    tagsName: string = '';
    filteredTags: OverlayTag[] = [];
    pinnedTabs: number[] = [0];
    activeTabIndex: number = 0;
    batchFontSize: 'sm' | 'md' | 'lg' | 'xl' = 'md';
    batchRowSize: 'sm' | 'md' | 'lg' | 'xl' = 'md';
    avatarSize: 'sm' | 'md' | 'lg' | 'xl' = 'md';
    inputSize:'sm'| 'md'| 'lg'= 'md';
    size: 'sm' | 'md' | 'lg' = 'md';
    buttonSize : 'small' | 'large' | 'medium' = 'medium';
    batchList: any[] = [];
    batchTableColumns: any[] = [];
    dynamicTabs: any[] = [];
    columnSidebarVisible: boolean = false;
    uploadSidebarVisible: boolean = false;
    selectionSidebarVisible: boolean = false;
    reworkDialogVisible: boolean = false;
    // API-driven data
    batchStatusList: any[] = [];
    batchStatusPanelList: any[] = [];
    batchStatusRowIndex: number = 0;
    batchLogList: any[] = [];
    commentsSidebarVisible: boolean = false;
    currentBatchComments: any[] = [];
    currentBatchId: string = '';
    currentBatchName: string = '';
    currentBatchHeader: string = '';
    selectedBatches: any[] = [];
    selectedBatchesComment: string = '';
    selectedBatchStatus: string = '';
    statusOptions: any[] = [];

    // API-driven suggestions
    mentionSuggestions: any[] = [];
    hashtagSuggestions: any[] = [];
    assigneeOptions: any[] = [];

    // Subscription management
    subscriptionId: string = '';

    // Loading states
    isLoading: boolean = false;
    isLoadingBatches: boolean = false;
    selectedBatchETA: string = '';
    selectedBatchAssignee: string = '';
    batchTableFilters = {};
    selectedItem: [] | undefined;
    selectedRowForTags: any;
    selectedDate: Date | null = null;
    previousDate: Date | null = null;
    currentRowData: any = null;
    isCreateTagSidebarOpen: boolean = false;
    openComment: boolean = false;

    // API-driven search items
    allItems: any[] = [];
    filteredItems: any[] = [];

    // API-driven tags
    overlayTags: OverlayTag[] = [];
    @ViewChild('batchStatusPanel') statusPanel: any;
    @ViewChild('batchLogPanel') batchLogPanel: any;
    @ViewChild('calendalPanel') calendalPanel: any;
    @ViewChild('tagsPanel') tagsPanel: any;



    constructor(
        private homeService: HomeService,
        private commentsService: CommentsService,
        private userService: UserService,
        private productsService: ProductsService,
        private productDetailsService: ProductDetailsService,
        private reviewService: ReviewService,
        private cdr: ChangeDetectorRef,
        private confirmationService: ConfirmationService,
        private router: Router,
        private route: ActivatedRoute
    ) {
        // Initialize with empty columns - will be loaded from API
        this.batchTableColumns = [];
    }

    ngOnInit() {
        this.initializeSubscriptionId();
        this.loadInitialData();
    }

    private initializeSubscriptionId() {
        // Get subscription ID from query params or localStorage
        this.route.queryParams.subscribe(params => {
            if (params['sub']) {
                this.subscriptionId = params['sub'];
                localStorage.setItem('SubscriptionID', this.subscriptionId);
            } else {
                this.subscriptionId = localStorage.getItem('SubscriptionID') || '';
            }
        });
    }

    private loadInitialData() {
        if (!this.subscriptionId) {
            console.error('No subscription ID available');
            return;
        }

        this.isLoading = true;

        // Load all initial data
        Promise.all([
            this.loadBatchList(),
            this.loadLabelList(),
            this.loadStats(),
            this.loadUserNamesToTag()
        ]).finally(() => {
            this.isLoading = false;
        });
    }

    private loadBatchList() {
        this.isLoadingBatches = true;
        return this.homeService.getBatchList(
            '1', // page
            '50', // size
            '', // status
            '', // search
            '', // start_date
            '', // end_date
            this.subscriptionId,
            [] // tags
        ).subscribe({
            next: (response) => {
                this.batchList = response.result || [];
                this.isLoadingBatches = false;
            },
            error: (error) => {
                console.error('Error loading batch list:', error);
                this.batchList = [];
                this.isLoadingBatches = false;
            }
        });
    }

    private loadLabelList() {
        return this.homeService.getLabelList(this.subscriptionId).subscribe({
            next: (response) => {
                this.overlayTags = response.map((label: any) => ({
                    name: label.name,
                    severity: this.mapLabelSeverity(label.type)
                }));
            },
            error: (error) => {
                console.error('Error loading label list:', error);
                this.overlayTags = [];
            }
        });
    }

    private loadStats() {
        return this.homeService.getStats(this.subscriptionId).subscribe({
            next: (response) => {
                // Handle stats data if needed
                console.log('Stats loaded:', response);
            },
            error: (error) => {
                console.error('Error loading stats:', error);
            }
        });
    }

    private loadUserNamesToTag() {
        return this.commentsService.getUserNamesToTag(this.subscriptionId).subscribe({
            next: (response) => {
                this.mentionSuggestions = response.map((user: any) => ({
                    name: user.name,
                    id: `@${user.username}`,
                    type: 'mention'
                }));
            },
            error: (error) => {
                console.error('Error loading user names:', error);
                this.mentionSuggestions = [];
            }
        });
    }

    private mapLabelSeverity(type: string): ChipSeverity {
        const severityMap: { [key: string]: ChipSeverity } = {
            'high': 'error',
            'medium': 'warning',
            'low': 'secondary',
            'info': 'primary'
        };
        return severityMap[type] || 'secondary';
    }

    openBatchDetails(batchData: any) {
        const oldTabIndex = this.dynamicTabs.findIndex(
            tab => tab.data.batchDetails.batch_id === String(batchData.batch_id)
        );
        if (oldTabIndex !== -1) {
            setTimeout(() => {
                this.activeTabIndex = oldTabIndex + 1;
                this.cdr.detectChanges();
            }, 0);
        } else {
            this.dynamicTabs.push({
                header: String(batchData.name),
                component: BatchRowsComponent,
                closable: true,
                data: { batchDetails: batchData },
            });
            this.cdr.detectChanges();
            setTimeout(() => {
                this.activeTabIndex = this.dynamicTabs.length;
                this.cdr.detectChanges();
            }, 0);
        }
    }

    onTabClose(event: { index: number }) {
        const index = event.index;
        this.dynamicTabs.splice(index - 1, 1);
        if (this.dynamicTabs.length) {
            if (index == this.activeTabIndex || index < this.activeTabIndex) {
                this.activeTabIndex = this.activeTabIndex - 1;
                this.cdr.detectChanges();
            }
        } else {
            this.activeTabIndex = 0;
            this.cdr.detectChanges();
        }
    }

    openColumnsList() {
        this.columnSidebarVisible = true;
    }

    get sortedSelectedColumns() {
        return this.batchTableColumns
            .filter((column: any) => column.selected)
            .sort((a: any, b: any) => a.order - b.order);
    }

    filterColumnsList(event: any) {
        this.batchTableColumns = event;
    }

    getBatchStatus(key: string) {
        return (
            this.batchStatusList.find(status => status.key === key) || {
                name: 'In Queue',
                key: 'in_queue',
                icon: 'cax cax-question-circle',
                severity: 'secondary',
            }
        );
    }

    openBatchStatusPanel(event: any, status: any, rowIndex: number) {
        this.statusPanel.hide();
        setTimeout(() => {
            this.batchStatusRowIndex = rowIndex;
            this.batchStatusPanelList = [];
            this.batchStatusPanelList = this.getBatchFilteredList(status);
            if (this.batchStatusPanelList.length) {
                this.statusPanel.show(event);
            }
        }, 200);
    }

    openBatchLogPanel(event: any, batchData: any) {
        this.batchLogPanel.hide();
        setTimeout(() => {
            this.batchLogList = batchData.audit_log;
            this.batchLogPanel.show(event);
        }, 200);
    }

    getBatchFilteredList(status: string) {
        const parentItem = this.batchStatusList.find(item => item.key === status);
        return parentItem?.child?.length
            ? parentItem.child
                  .map(childKey =>
                      this.batchStatusList.find(item => item.key === childKey)
                  )
                  .filter(Boolean)
            : [];
    }

    updateBatchStatus(status: any) {
        if (status.key === 'cancelled') {
            this.confirmationService.confirm({
                message: 'Are you sure you want to cancel the batch',
                header: 'Are you sure?',
                acceptLabel: 'Yes, cancel batch',
                rejectLabel: 'Cancel',
                acceptButtonStyleClass: 'cax-button-danger',
                rejectButtonStyleClass:
                    'cax-button-secondary cax-button-outlined',
                closable: true,
                headerIcon: 'cax cax-close-circle',
                headerIconStyle: { color: 'var(--error-500)' },
                accept: () => {
                    this.batchList[this.batchStatusRowIndex].status =
                        status.key;
                },
            });
        } else if (status.key === 'approved') {
            this.confirmationService.confirm({
                message:
                    "Are you sure you want to change status to 'Approved'?",
                header: 'Are you sure?',
                acceptLabel: 'Yes',
                rejectLabel: 'Cancel',
                rejectButtonStyleClass:
                    'cax-button-secondary cax-button-outlined',
                closable: true,
                headerIcon: 'cax cax-info-circle',
                headerIconStyle: { color: 'var(--primary-500)' },
                accept: () => {
                    this.batchList[this.batchStatusRowIndex].status =
                        status.key;
                },
            });
        } else if (status.key === 'rework') {
            this.reworkDialogVisible = true;
        } else {
            this.batchList[this.batchStatusRowIndex].status = status.key;
        }
        this.statusPanel.hide();
    }

    openComments(rowData: any) {
        this.currentBatchId = rowData.batch_id;
        this.currentBatchName = rowData.name;
        this.currentBatchHeader = `${rowData.name} (${rowData.batch_id})`;
        this.loadBatchComments(rowData.batch_id);
        this.commentsSidebarVisible = true;
        this.openComment = true;
    }

    private loadBatchComments(batchId: string) {
        this.commentsService.getCommentsList(
            this.subscriptionId,
            '1', // page
            '50', // size
            'batch', // category
            batchId, // id
            '', // q
            '' // comment_thread
        ).subscribe({
            next: (response) => {
                this.currentBatchComments = response.result || [];
            },
            error: (error) => {
                console.error('Error loading comments:', error);
                this.currentBatchComments = [];
            }
        });
    }

    onCommentAdded(comment: any) {
        // Post comment via API
        const commentData = {
            text: comment.text,
            mentions: comment.mentions || [],
            hashtags: comment.hashtags || []
        };

        this.commentsService.postComment(
            this.subscriptionId,
            'batch',
            this.currentBatchId,
            commentData
        ).subscribe({
            next: (response) => {
                // Reload comments to get the updated list
                this.loadBatchComments(this.currentBatchId);
            },
            error: (error) => {
                console.error('Error posting comment:', error);
            }
        });
    }

    openUploadSidebar() {
        this.uploadSidebarVisible = true;
    }

    onRowHeightChange(event: 'sm' | 'md' | 'lg' | 'xl' ) {
        this.batchRowSize = event;
        this.avatarSize = event;
        this.inputSize = event === 'xl' ? 'lg' : event;
        this.size = event === 'xl' ? 'lg' : event;

        const buttonSizeMap: { [key in 'sm' | 'md' | 'lg' | 'xl']: 'small' | 'medium' | 'large' } = {
        sm: 'small',
        md: 'medium',
        lg: 'large',
        xl: 'large', // xl maps to large as well
    };

    this.buttonSize = buttonSizeMap[event];

    }

    onFontSizeChange(event: 'sm' | 'md' | 'lg' | 'xl') {
        this.batchFontSize = event;
    }

    onTableSelectionChange(event: any) {
        this.selectedBatches = event;
        this.selectionSidebarVisible = this.selectedBatches.length > 0;

        // Clear input fields when selection changes
        if (this.selectedBatches.length > 0) {
            this.selectedBatchStatus = '';
            this.selectedBatchETA = '';
            this.selectedBatchAssignee = '';
            this.selectedBatchesComment = '';
        }
    }

    onTableSort(event: any) {
        console.log(event);
    }

    onTableFilter(event: any) {
        console.log(event);
        this.batchTableFilters = event.filters;
    }

    get appliedTableFilters() {
        return Object.fromEntries(
            Object.entries(this.batchTableFilters).filter(
                // eslint-disable-next-line @typescript-eslint/no-unused-vars
                ([key, value]: any) => value.matchMode !== null
            )
        );
    }

    onMultipleCommentsAdded(comment: string) {
        if (!comment || !comment.trim()) return;

        const commentData = {
            text: comment,
            mentions: [],
            hashtags: []
        };

        // Post comments to all selected batches via API
        this.selectedBatches.forEach(batch => {
            this.commentsService.postComment(
                this.subscriptionId,
                'batch',
                batch.batch_id,
                commentData
            ).subscribe({
                next: () => {
                    console.log(`Comment added to batch ${batch.batch_id}`);
                },
                error: (error) => {
                    console.error(`Error adding comment to batch ${batch.batch_id}:`, error);
                }
            });
        });

        // Reload batch list after a short delay
        setTimeout(() => {
            this.loadBatchList();
        }, 1000);

        // Clear the comment input
        this.selectedBatchesComment = '';
    }

    updateMultipleBatchStatus(status: string) {
        if (!status) return;

        // Update status via API for all selected batches
        this.selectedBatches.forEach(batch => {
            this.homeService.statusUpdate(
                this.subscriptionId,
                batch.batch_id,
                status
            ).subscribe({
                next: () => {
                    console.log(`Status updated for batch ${batch.batch_id}`);
                },
                error: (error) => {
                    console.error(`Error updating status for batch ${batch.batch_id}:`, error);
                }
            });
        });

        // Reload batch list after a short delay
        setTimeout(() => {
            this.loadBatchList();
        }, 1000);
    }

    updateMultipleBatchETA(eta: string) {
        if (!eta) return;

        this.selectedBatches.forEach(batch => {
            const batchIndex = this.batchList.findIndex(
                item => item.batch_id === batch.batch_id
            );

            if (batchIndex !== -1) {
                this.batchList[batchIndex].eta = eta;
            }
        });

        // Clear the input field after update
        this.selectedBatchETA = '';
    }

    updateMultipleBatchAssignee(assignee: string) {
        if (!assignee) return;

        this.selectedBatches.forEach(batch => {
            const batchIndex = this.batchList.findIndex(
                item => item.batch_id === batch.batch_id
            );

            if (batchIndex !== -1) {
                this.batchList[batchIndex].assignee = assignee;
            }
        });

        // Clear the input field after update
        this.selectedBatchAssignee = '';
    }

    isSaveEnabled(): boolean {
        const hasStatusChange = !!this.selectedBatchStatus;
        const hasAssigneeChange = !!this.selectedBatchAssignee;
        const hasCommentChange =
            !!this.selectedBatchesComment &&
            this.selectedBatchesComment.trim() !== '';

        return hasStatusChange || hasAssigneeChange || hasCommentChange;
    }

    getBatchesToUncheck(): string[] {
        return this.selectedBatches.map(batch => batch.batch_id);
    }

    saveAllChanges() {
        // Check if any changes have been made
        const hasStatusChange = !!this.selectedBatchStatus;
        const hasETAChange = !!this.selectedBatchETA;
        const hasAssigneeChange = !!this.selectedBatchAssignee;
        const hasCommentChange =
            !!this.selectedBatchesComment &&
            this.selectedBatchesComment.trim() !== '';

        if (
            !hasStatusChange &&
            !hasETAChange &&
            !hasAssigneeChange &&
            !hasCommentChange
        ) {
            return;
        }
        const batchesToUncheck = this.getBatchesToUncheck();

        // Show confirmation dialog
        this.confirmationService.confirm({
            message: `Are you sure you want to apply changes to ${this.selectedBatches.length} selected batches?`,
            header: 'Confirm Changes',
            acceptLabel: 'Yes, Save Changes',
            rejectLabel: 'Cancel',
            rejectButtonStyleClass: 'cax-button-secondary cax-button-outlined',
            closable: true,
            headerIcon: 'cax cax-info-circle',
            headerIconStyle: { color: 'var(--primary-500)' },
            accept: () => {
                // Apply all changes
                if (hasStatusChange) {
                    this.updateMultipleBatchStatus(this.selectedBatchStatus);
                }

                if (hasETAChange) {
                    this.updateMultipleBatchETA(this.selectedBatchETA);
                }

                if (hasAssigneeChange) {
                    this.updateMultipleBatchAssignee(
                        this.selectedBatchAssignee
                    );
                }

                if (hasCommentChange) {
                    this.onMultipleCommentsAdded(this.selectedBatchesComment);
                }
                this.selectedBatchStatus = '';
                this.selectedBatchETA = '';
                this.selectedBatchAssignee = '';
                this.selectedBatchesComment = '';
                console.log('Changes saved successfully!');
                this.uncheckSelectedItems(batchesToUncheck);
                this.selectedBatches = [];
                this.selectionSidebarVisible = false;
            },
        });
    }

    uncheckSelectedItems(batchIds: string[]) {
        this.selectedBatches = this.selectedBatches.filter(
            batch => !batchIds.includes(batch.batch_id)
        );

        // Hide selection sidebar if no items remain selected
        if (this.selectedBatches.length === 0) {
            this.selectionSidebarVisible = false;
        }
    }

    handleTagRemove(event: MouseEvent, rowData: any, tag: any): void {
        event.stopPropagation();
        const tagIndex = rowData.tags.findIndex((t: any) => t.name === tag.name);
        if (tagIndex !== -1) {
            rowData.tags.splice(tagIndex, 1);
        }
    }

    onTagSearch(searchTerm: string): void {
        if (!searchTerm || searchTerm.trim() === '') {
            this.filteredTags = [...this.overlayTags];
        } else {
             const query = searchTerm.toLowerCase().trim();
            this.filteredTags = this.overlayTags.filter(tag =>
                tag.name.toLowerCase().includes(query)
            );
        }
    }

    handleOverlay(event: MouseEvent, rowData: any, tag: any) {
    this.selectedRowForTags = rowData;
    this.filteredTags = [...this.overlayTags];
    this.tagsName = '';

    this.tagsPanel.toggle(event);


    setTimeout(() => {
        const overlayEl = this.tagsPanel?.container;

        if (overlayEl) {
            if (this.overlayTags.length === 0) {
                overlayEl.classList.add('no-tags-overlay-bg');
            } else {
                overlayEl.classList.remove('no-tags-overlay-bg');
            }
        }
    }, 0);
}

    updateTag(selectedTag: any): void {
        if (!this.selectedRowForTags) return;

        // Use API to add label to batch
        this.homeService.addLabel(
            this.subscriptionId,
            this.selectedRowForTags.batch_id,
            selectedTag.id || selectedTag.name,
            '' // remove_label_id (empty for adding)
        ).subscribe({
            next: () => {
                console.log(`Tag added to batch ${this.selectedRowForTags.batch_id}`);
                // Reload batch list to get updated tags
                this.loadBatchList();
            },
            error: (error) => {
                console.error('Error adding tag to batch:', error);
            }
        });

        this.tagsName = '';
        this.filteredTags = [...this.overlayTags];

        this.tagsPanel.hide();
    }

    openCreateTagSidebar(){
        this.isCreateTagSidebarOpen = true;
        this.tagsPanel.hide();
    }

    search(event: any) {
        const query = event.query.toLowerCase();

        if (!query) {
            this.filteredItems = [...this.allItems];
            return;
        }

        // Search through batch list for matching items
        const batchMatches = this.batchList
            .filter((batch: any) =>
                batch.name?.toLowerCase().includes(query) ||
                batch.batch_id?.toLowerCase().includes(query) ||
                batch.description?.toLowerCase().includes(query)
            )
            .map((batch: any) => ({
                title: batch.name,
                batchId: batch.batch_id
            }));

        this.filteredItems = batchMatches.length > 0 ? [
            {
                label: 'Batches',
                items: batchMatches
            }
        ] : [];
    }

    onItemSelect(event: any) {
        console.log('Selected item:', event);
    }

    onDateClick(event: Event, rowData: any): void {
        // Initialize with today's date if no date is present
        this.previousDate = rowData.eta ? new Date(rowData.eta) : null;
        this.selectedDate = rowData.eta ? new Date(rowData.eta) : new Date();
        this.currentRowData = rowData; // Store reference to the current row
        this.calendalPanel.toggle(event);
    }
    handleDateSaved(): void {
        if (this.currentRowData && this.selectedDate) {
            // console.log('Updating ETA for row:', this.currentRowData);
            // Update the ETA field in the current row
            this.currentRowData.eta = this.selectedDate;
        }
        this.calendalPanel.hide();
    }
    // Handle date cancel
    handleDateCancel(): void {
        this.selectedDate = this.previousDate;
        this.calendalPanel.hide();
    }

    onStatusChange(row: any) {
        console.log('Status changed:', row.status);
        // Update status via API
        this.homeService.statusUpdate(
            this.subscriptionId,
            row.batch_id,
            row.status
        ).subscribe({
            next: () => {
                console.log(`Status updated for batch ${row.batch_id}`);
            },
            error: (error) => {
                console.error(`Error updating status for batch ${row.batch_id}:`, error);
            }
        });
    }

    onDescriptionChange(row: any) {
        console.log('Description updated:', row.description);
        // Persist if necessary - implement API call if available
    }


}
